// React + ag-grid
import { useNavigate } from "@remix-run/react";
import debounce from "lodash/debounce";
import isEmpty from "lodash/isEmpty";
import uniq from "lodash/uniq";
import { useEffect, useState } from "react";
// Organisms
import { KanbanList } from "~/shared/components/organisms/kanbanView";
// Constants
import { STATUS_CODE } from "~/shared/constants";
import { getValuableObj } from "~/shared/utils/helper/getValuebleObj";
// Redux
import Sortable from "sortablejs";
import {
  updateKanbanSettingApi,
  updateKanbanSortingApi,
} from "~/redux/action/kanbanSettings";
import { routes } from "~/route-services/routes";
import { getGConfig, getGModuleFilters } from "~/zustand";
import { getPOKanbanListApi } from "../../redux/action/dashboardAction";
import { updatePODetailApi } from "../../redux/action/PODetailAction";
import POListAction from "./POListAction";
import { ReadOnlyPermissionMsg } from "~/shared/components/molecules/readOnlyPermissionMsg";
import { escapeHtmlEntities } from "~/helpers/helper";
import { useAppPODispatch } from "../../redux/store";
import { updatePODetail } from "../../redux/slices/poDetailSlice";

const PurchaseOrderKanban = ({
  kanbanSetting,
  setKanbanSetting,
  kanbanSelected,
  setKanbanSelected,
  search,
  isReadOnly,
}: IPOKanbanProps) => {
  const navigate = useNavigate();
  const { module_id, module_access }: GConfig = getGConfig();
  const dispatch = useAppPODispatch();
  const filter =
    (getGModuleFilters() as Partial<PurchaseOrdersFilter> | undefined) || {};
  // States
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [selectedData, setSelectedData] = useState<IPODetailData | null>();
  const [selectedPOData, setSelectedPOData] = useState<IPODetailData>();
  const [kanbanListData, setKanbanListData] = useState<IPOKanbanData[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [loadingChild, setLoadingChild] = useState<boolean>(false);
  const [pageBySection, setPageBySection] = useState<{
    [key: string]: number;
  }>({});
  const [hasMoreBySection, setHasMoreBySection] = useState<
    Record<string, boolean>
  >({});
  const [loadingBySection, setLoadingBySection] = useState<{
    [key: string]: boolean;
  }>({});
  const stageFilterKeys = (
    filter?.billing_status_kanban?.split(",") || []
  ).filter((key) => key.trim() !== "");
  // fetch kanban to do list
  const fetchKanbanPOList = async (
    type: string,
    isLoad: boolean = true,
    hideLoader?: boolean
  ) => {
    if (!hideLoader) {
      type === "" ? setIsLoading(true) : setLoadingChild(isLoad);
    }
    // type === "" ? setIsLoading(true) : setLoadingChild(isLoad);
    const pageForType = pageBySection[type] || 0;
    const filterObj = !isEmpty(filter) ? getValuableObj(filter) : undefined;
    if (filterObj?.status === STATUS_CODE.ALL) {
      delete filterObj.status;
    }

    if (!filter) return;

    delete filterObj?.billing_status;
    delete filterObj?.billing_status_names;

    if (
      filterObj?.is_multiple_suppliers &&
      filterObj?.is_multiple_suppliers == "2"
    )
      delete filterObj?.is_multiple_suppliers;

    let dataParams = {
      // start: pageForType * 15,
      limit: 15,
      is_kanban: true,
      start: pageForType,
      ignore_filter: 1, // data-base
      search: escapeHtmlEntities(search),
      filter: !isEmpty(filterObj) ? filterObj : undefined,
      any_status: !!type ? Number(type) : undefined,
    };

    try {
      const resData = (await getPOKanbanListApi(dataParams)) as IPOKanbanApiRes;

      if (resData?.success) {
        // setIsLoading(false);
        const newTypes = (resData?.data || [])
          // ?.filter((data) => data.key !== "invoice_charge")
          ?.map((data) => {
            return {
              ...data,
              kanban_data: (data?.kanban_data ?? [])?.map((kanbanData) => {
                return {
                  ...kanbanData,
                  company_purchase_order_id:
                    !!kanbanData.prefix_company_purchase_order_id
                      ? `${kanbanData.prefix_company_purchase_order_id}`
                      : "",
                  // total: formatter((Number(kanbanData?.total) / 100).toFixed(2))
                  //   .value_with_symbol,
                  // amount: formatter(
                  //   (
                  //     Number(
                  //       inv_total_balance_due == 1
                  //         ? Number(kanbanData?.total) -
                  //         Number(kanbanData?.payment_amount)
                  //         : kanbanData?.total
                  //     ) / 100
                  //   ).toFixed(2)
                  // ).value_with_symbol,
                };
              }),
            };
          });
        const newHasMoreBySection = newTypes?.reduce(
          (acc: Record<number, boolean>, section: IPOKanbanData) => {
            const sectionDataLength = section.kanban_data.length;
            acc[section.item_id] = sectionDataLength >= 15;
            return acc;
          },
          {}
        );

        setHasMoreBySection((prev) => ({
          ...prev,
          ...newHasMoreBySection,
        }));
        if (pageForType == 0) {
          setKanbanListData(newTypes);
        } else {
          setKanbanListData((prevData) => {
            if (prevData.length === 0) {
              return newTypes;
            }
            const updateData = prevData.map((prevSection) => {
              const newSection = newTypes.find(
                (d) => d.item_id === prevSection.item_id
              );
              if (newSection) {
                const updatedSection = { ...prevSection };

                const newKanbanData = newSection.kanban_data.filter(
                  (newItem) =>
                    !updatedSection.kanban_data.some(
                      (existingItem) =>
                        existingItem.purchase_order_id ===
                        newItem.purchase_order_id
                    )
                );
                updatedSection.kanban_data.push(...newKanbanData);
                return updatedSection;
              }
              return prevSection;
            });
            return updateData;
          });
        }

        setKanbanSelected(
          stageFilterKeys.length > 0
            ? stageFilterKeys
            : resData?.kanban_estimate_type_selected?.length > 0
            ? resData?.kanban_estimate_type_selected?.map((data) =>
                data.toString()
              )
            : ([
                "86",
                "87",
                "141",
                "146",
                "88",
                "372",
                "373",
                "374",
                "375",
                "85",
                "190",
              ] as string[])
        );
        setKanbanSetting(resData?.kanban_setting);
      } else {
        setKanbanListData([]);
        setKanbanSetting(undefined);
      }

      if (type !== "") {
        setLoadingBySection((prev) => ({
          ...prev,
          [type]: false,
        }));
      }
    } catch (err) {
      setIsLoading(false);
      notification.error({
        description: (err as Error)?.message || "Something went wrong!",
      });
    } finally {
      setIsLoading(false);
      setLoadingChild(false);
    }
  };

  const debouncedFetch = debounce(() => {
    fetchKanbanPOList("");
  }, 100);

  useEffect(() => {
    if (!isEmpty(filter)) {
      debouncedFetch();
    }
  }, [search, JSON.stringify(filter)]);

  const handleLoadMore = (val: number) => {
    const isSectionLoading = loadingBySection[val];
    if (isSectionLoading) return;

    const hasMoreForSection = hasMoreBySection[val];
    const currentSectionPage = pageBySection[val] || 0;
    if (hasMoreForSection) {
      const nextPage = currentSectionPage + 1;
      setPageBySection((prev) => ({
        ...prev,
        [val]: nextPage,
      }));
      if (currentSectionPage !== 0) {
        setLoadingBySection((prev) => ({
          ...prev,
          [val]: true,
        }));
        fetchKanbanPOList(val.toString());
      }
    }
  };

  const handleColspan = async (
    columnId: string,
    isCollapseCard: string,
    key: string
  ) => {
    // Optimistically update the kanbanSelected state

    const updatedKanbanSelected = uniq(
      stageFilterKeys?.length > 0
        ? !kanbanSelected.includes(columnId) && !kanbanSelected.includes(key)
          ? [...kanbanSelected, columnId]
          : kanbanSelected.filter(
              (value) => value !== columnId && value !== key
            )
        : kanbanSelected.includes(columnId)
        ? kanbanSelected.filter((value) => value !== columnId)
        : [...kanbanSelected, columnId]
    ).filter((data) => data.trim() !== "");

    setKanbanSelected(updatedKanbanSelected);
    if (stageFilterKeys.length > 0) {
      return;
    }
    const requestKanbanSetting = {
      module_field_id: updatedKanbanSelected.length
        ? updatedKanbanSelected
        : undefined,
      default_view: kanbanSetting?.default_view?.toString() ?? "0",
      module_id,
    };

    try {
      const response = (await updateKanbanSettingApi(
        requestKanbanSetting
      )) as IKanbanSettingApiRes;

      if (response.success) {
        setKanbanSetting(response.data);
        setKanbanSelected(response.kanban_project_selected);
      } else {
        // Revert the optimistic update on failure
        setKanbanSelected(kanbanSelected);
        notification.error({
          description: response.message || "Something went wrong!",
        });
      }
    } catch (error) {
      // Revert the optimistic update on error
      setKanbanSelected(kanbanSelected);
      notification.error({
        description: (error as Error)?.message || "Something went wrong!",
      });
    }
  };

  const handleInvoiceColumn = async (event: Sortable.SortableEvent) => {
    setIsDragging(true);
    const currentArray = kanbanListData?.map((data) => ({
      column_id: data.item_id,
      sort_order: Number(data.sort_order),
      sorting_id: data.sorting_id.toString(),
      column_name: data.name,
      type_id: data.item_id.toString(),
    }));

    const kanban_sorting: IKanbanSortingArray[] = currentArray.map(
      (data, index) => ({
        ...data,
        sort_order: index,
      })
    );

    try {
      const requestKanbanSetting: IKanbanSorting = {
        kanban_sorting: kanban_sorting,
        module_id: module_id,
      };
      const responseKanbanSetting = (await updateKanbanSortingApi(
        requestKanbanSetting
      )) as IKanbanSortingApiRes;
      if (!responseKanbanSetting.success) {
        notification.error({
          description: responseKanbanSetting.message || "Something went wrong!",
        });
      }
    } catch (error) {
      notification.error({
        description: (error as Error).message || "Something went wrong!",
      });
    } finally {
      setIsDragging(false);
    }
  };

  // const { customStatusList }: ICustomStatusListInitialState = useAppIVSelector(
  //   (state) => state.customStatusListData
  // );

  const handlePOCardDragAndDrop = async (event: Sortable.SortableEvent) => {
    const { from, to } = event;
    const fromColumnId =
      from?.closest("[data-id]")?.getAttribute("data-id") ?? "";
    const toColumnId = to?.closest("[data-id]")?.getAttribute("data-id") ?? "";
    // const isValid = await validateCardDragAndDrop(
    //   fromColumnId,
    //   toColumnId,
    //   selectedData
    // );
    if (fromColumnId !== toColumnId && !!selectedData?.purchase_order_id) {
      try {
        const payload = {
          purchase_order_id: selectedData?.purchase_order_id,
          billing_status: Number(toColumnId),
          is_multiple_suppliers: selectedData?.is_multiple_suppliers,
          supplier_id: selectedData?.supplier_id || 0,
          supplier_contact_id: selectedData?.supplier_contact_id || 0,
          po_suppliers: selectedData?.po_suppliers,
        };
        const updateStatusRes = (await updatePODetailApi(
          payload
        )) as IEDetailsApiRes;
        console.log(fromColumnId, toColumnId, updateStatusRes, "from to...");
        if (updateStatusRes?.success) {
          const detail = updateStatusRes?.data?.detail;
          console.log(detail, "detail");
          if (Object.keys(detail || {})?.length > 0) {
            dispatch(updatePODetail(detail));
          }
          setKanbanListData((prevData) =>
            prevData.map((item) => {
              if (item.item_id == Number(toColumnId)) {
                console.log({
                  ...item,
                  ...payload,
                  total_count: (parseInt(item.total_count) + 1).toString(),
                });
                return {
                  ...item,
                  ...payload,
                  total_count: (parseInt(item.total_count) + 1).toString(),
                };
              } else if (item.item_id == Number(fromColumnId)) {
                return {
                  ...item,
                  billing_status: Number(fromColumnId),
                  total_count: (parseInt(item.total_count) - 1).toString(),
                };
              }
              return item;
            })
          );
        } else {
          fetchKanbanPOList("", false, true);
          notification.error({
            description: updateStatusRes.message || "Something went wrong!",
          });
        }
      } catch (error) {
        notification.error({
          description: (error as Error).message || "Something went wrong!",
        });
      }
    } else {
      fetchKanbanPOList("", false, true);
    }
  };

  // const validateCardDragAndDrop = async (
  //   fromId: string,
  //   toId: string,
  //   selectedData: IPOKanbanRowDataList | null | undefined
  // ) => {
  //   if (!selectedData || !fromId || !toId) {
  //     return false;
  //   }
  //   const e_sender_status_id = customStatusList?.find(
  //     (data) => data.item_id.toString() == fromId
  //   )?.key;
  //   const status_id = customStatusList?.find(
  //     (data) => data.item_id.toString() == toId
  //   )?.key;

  //   if (
  //     status_id &&
  //     e_sender_status_id &&
  //     (invoiceAgingKeys.includes(status_id) ||
  //       (invoiceAgingKeys.includes(e_sender_status_id) &&
  //         status_id == "invoice_submitted"))
  //   ) {
  //     notification.error({
  //       description:
  //         "You can't move aging record from other status or other record in aging status.",
  //     });
  //     return false;
  //   } else if (!selectedData.due_date && status_id == "invoice_submitted") {
  //     notification.error({
  //       description:
  //         "You can't change status, because due date is blank and that need to be required to change status.",
  //     });
  //     return false;
  //   } else if (status_id == "invoice_paid") {
  //     let paymentTotal = Number(selectedData.payment_amount);
  //     let invTotal = Number(unformatted(selectedData.total)) * 100;

  //     if (invTotal > paymentTotal) {
  //       notification.error({
  //         description:
  //           "This invoice cannot be marked as Paid as there is an outstanding balance against it. A Payment must be posted to bring the balance to 0.",
  //       });
  //       return false;
  //     } else {
  //       return true;
  //     }
  //   } else {
  //     return true;
  //   }
  // };

  return (
    <div className="pt-[41px] md:h-[calc(100dvh-116px)] h-[calc(100dvh-84px)] overflow-y-auto overflow-hidden">
      <ReadOnlyPermissionMsg
        view={isReadOnly}
        className="px-4 sm:pt-4 pt-1"
        textClassName="sm:text-13 text-xs flex justify-center items-end sm:h-auto h-7 sm:leading-auto leading-[15px]"
      />
      <div className="flex pt-4 pb-2.5 px-1.5 transition-all ease-in-out duration-300 overflow-x-auto">
        <KanbanList
          list={kanbanListData} //  kanban data
          setList={setKanbanListData}
          loading={isLoading}
          childLoader={loadingChild}
          loadMore={(val) => {
            handleLoadMore(val);
          }}
          collapseClick={(poColumn) => {
            handleColspan(
              poColumn?.item_id?.toString(),
              poColumn?.is_collapse_card?.toString(),
              poColumn?.key?.toString()
            );
          }}
          cardDetailsClick={(po) => {
            if (!!po?.purchase_order_id) {
              navigate(
                `${routes.MANAGE_PURCHASE_ORDERS.url}/${po.purchase_order_id}`
              );
            }
          }}
          kanbanSelected={kanbanSelected}
          handleCardDragDropEnd={handlePOCardDragAndDrop}
          handleColumnDragDropEnd={handleInvoiceColumn}
          isReadOnly={isDragging || isReadOnly}
          handleMouseMove={(po) => {
            setSelectedData(po);
          }}
          onActionClick={(po) => {
            setSelectedPOData(po);
          }}
          colum={{
            headerName: "name",
            parentId: "item_id",
            count: "total_count",
            collapse: "is_collpase_card",
            color: "status_color",
            child: "kanban_data",
            childCard: {
              cardId: "purchase_order_id", // child card id pass
              cardFirstFirst: "supplier_name",
              cardFirstSecondPrefix: "",
              cardFirstSecond: "order_date",
              cardMiddleFirst: "subject",
              cardMiddleSecond: "company_purchase_order_id",
              cardLastFirst: "project_name",
            },
          }}
        >
          {module_access !== "read_only" && (
            <POListAction
              paramsData={selectedPOData}
              isDashboard={true}
              icon="fa-solid fa-ellipsis-h"
              pId={selectedPOData?.purchase_order_id}
              isBilled={selectedPOData?.is_billed}
              refreshAgGrid={() => fetchKanbanPOList("")}
              POID={
                !selectedPOData?.custom_purchase_order_id?.toString() ||
                selectedPOData?.custom_purchase_order_id?.length < 1
                  ? selectedPOData?.company_purchase_order_id?.toString()
                  : selectedPOData?.custom_purchase_order_id?.toString() || ""
              }
            />
          )}
        </KanbanList>
      </div>
    </div>
  );
};
export default PurchaseOrderKanban;
