// Molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { SelectField } from "~/shared/components/molecules/selectField";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { DatePickerField } from "~/shared/components/molecules/datePickerField";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { DirectoryFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/directoryFieldRedirectionIcon";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
import ContactDetailsModal from "~/shared/components/organisms/contactDetailModal/ContactDetailModal";
// Hook
import { useTranslation } from "~/hook";
import { useParams } from "@remix-run/react";
import { getGConfig, getGSettings } from "~/zustand";
import { useAppPODispatch, useAppPOSelector } from "../../../redux/store";
import {
  backendDateFormat,
  displayDateFormat,
} from "~/shared/utils/helper/defaultDateFormat";
import { getStatusForField } from "~/shared/utils/helper/common";
import useFieldStatus from "../../../utils/useFieldStatus ";
import { POfieldStatus } from "../../../utils/common";
import dayjs from "dayjs";
import {
  getPODetailNotReload,
  updatePODetailApi,
} from "../../../redux/action/PODetailAction";
import { updatePODetail } from "../../../redux/slices/poDetailSlice";
import delay from "lodash/delay";
import { replaceDOMParams, sanitizeString } from "~/helpers/helper";
import { useEffect, useMemo, useState } from "react";
import { getDirectaryKeyById } from "~/components/sidebars/multi-select/customer/zustand/action";
import { PODetailsFields, shipToOption } from "../../../utils/constants";
import AddressInformationField from "./AddressInfoField";
import { HtmlDecode } from "../../../utils/function";

const ShippingCard = ({ isReadOnly }: POReadOnlyComponent) => {
  const { _t } = useTranslation();
  const { id: purchase_order_id }: RouteParams = useParams();
  const { date_format }: GSettings = getGSettings();
  const dispatch = useAppPODispatch();
  const gConfig: GConfig = getGConfig();
  // const { formatter } = useCurrencyFormatter();
  const { purchaseOrderDetail, isPODetailLoading } = useAppPOSelector(
    (state) => state?.purchaseOrderDetail
  );
  const { loadingStatus, handleChangeFieldStatus, setLoadingStatus } =
    useFieldStatus(POfieldStatus);
  const [inputValues, setInputValues] =
    useState<IPODetailData>(PODetailsFields);
  // const [contactId, setcontactId] = useState<number>();
  const [additionalContact, setAdditionContact] = useState<number>(0);
  // const [isContactDetails, setIsContactDetails] = useState<boolean>(false);
  const [isOpenShipToContact, setIsOpenShipToContact] =
    useState<boolean>(false);
  const [isOpenProjectManagerDetails, setIsOpenProjectManagerDetails] =
    useState<boolean>(false);
  // const totalPOAmount = useMemo(() => {
  //   const nValue = Number(purchaseOrderDetail?.total) / 100;
  //   return formatter(
  //     Number(nValue) == 0
  //       ? Number(nValue)?.toFixed(0)
  //       : Number(nValue)?.toFixed(2)
  //   )?.value_with_symbol;
  // }, [purchaseOrderDetail]);
  useEffect(() => {
    if (purchaseOrderDetail) setInputValues(purchaseOrderDetail);
  }, [purchaseOrderDetail]);

  const shipToContactOptionKey: CustomerEmailTab[] = useMemo(() => {
    const keys: CustomerEmailTab[] = [
      CFConfig.employee_key,
      "my_crew",
      CFConfig.customer_key,
      CFConfig.lead_key,
      CFConfig.contractor_key,
      CFConfig.vendor_key,
      CFConfig.misc_contact_key,
      "by_service",
    ];
    if (!!purchaseOrderDetail?.pro_id && purchaseOrderDetail?.pro_id != 0) {
      keys.push("my_project" as CustomerEmailTab);
    }
    return keys;
  }, [purchaseOrderDetail]);

  const handleInpOnChange = (
    e: React.ChangeEvent<HTMLTextAreaElement | HTMLInputElement>,
    key: string
  ) => {
    const { name, value } = e.target;
    setInputValues({ ...inputValues, [key]: value });
  };

  const handleUpdateField = async (data: IPODetailData) => {
    const field = Object.keys(data)[0];
    const values = Object.values(data)[0] as string;
    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });

    const updateRes = (await updatePODetailApi({
      ...data,
      purchase_order_id,
    })) as IPODetailApiRes;

    if (updateRes?.success) {
      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });

      if (["delivery_date"]?.includes(field)) {
        dispatch(
          updatePODetail({
            // ...data,
            [field]: !!values ? dayjs(values).format(date_format) : "",
          })
        );
        // dispatch(getPODetail({ purchase_order_id }));
      } else {
        if (["ship_to_contact"]?.includes(field)) {
          const response = await dispatch(
            getPODetailNotReload({ purchase_order_id })
          );
          const payload = response.payload as { data: IPODetailData };
          if (payload && payload.data?.purchase_order_id) {
            const datas: IPODetailData = payload.data;
            await dispatch(
              updatePODetail({ ...datas, ...(updateRes?.data ?? {}) })
            );
          }
        }
        // else if (field === "ship_to") {
        //   await dispatch(
        //     updatePODetail({
        //       ...data,
        //       po_address1: null,
        //       po_address2: null,
        //       po_city: null,
        //       po_state: null,
        //       po_zip: null,
        //       ...(updateRes?.data ?? {}),
        //     })
        //   );
        // }
        else {
          await dispatch(
            updatePODetail({ ...data, ...(updateRes?.data ?? {}) })
          );
        }
      }
      const detail = updateRes?.data?.detail;
      if (Object.keys(detail || {})?.length > 0) {
        dispatch(updatePODetail(detail));
      }
    } else {
      handleChangeFieldStatus({
        field: field,
        status: "error",
        action: "API",
      });

      dispatch(
        updatePODetail({
          [field]: purchaseOrderDetail?.[field as keyof IPODetailData],
        })
      );
      // setInputValues({ ...purchaseOrderDetail, [field]: purchaseOrderDetail[field] });

      notification.error({
        description: updateRes?.message,
      });
    }

    resetFieldStatus(field);
  };

  const resetFieldStatus = (field: string) => {
    delay(() => {
      handleChangeFieldStatus({
        field: field,
        status: "button",
        action: "API",
      });
    }, 3000);
  };

  const handleChangeDate = (dateString: string, key: string) => {
    const formatDate = (date: string, type: string) => {
      return backendDateFormat(date, date_format);
    };
    setInputValues({
      ...inputValues,
      [key]: dateString,
    });
    const formattedDate =
      dateString && dateString !== "" ? formatDate(dateString, key) : null;
    if (purchaseOrderDetail?.[key as keyof IPODetailData] !== dateString) {
      handleUpdateField({
        [key]: formattedDate,
        purchase_order_id: Number(purchase_order_id) || 0,
        // customer_id: purchaseOrderDetail?.customer_id,
      });
    } else {
      dispatch(
        updatePODetail({
          [key]: purchaseOrderDetail?.[key as keyof IPODetailData],
        })
      );
    }
  };

  const handleShiptoContact = (data: Partial<TselectedContactSendMail>) => {
    if (data?.user_id !== undefined) {
      setInputValues({
        ...inputValues,
        ship_to_contact: data?.user_id,
        ship_to_contact_name: data?.display_name,
        // issued_by_type:
        //   data?.orig_type?.toString() ||
        //   getDirectaryIdByKey(data.type_key as CustomerTabs, gConfig),
      });
      handleUpdateField({
        ship_to_contact: data?.user_id,
        ship_to_contact_additional_contact_id: data?.contact_id,
        // issued_by_contact: data?.contact_id?.toString(),
      });
    } else {
      handleUpdateField({ ship_to_contact: "" });
      setInputValues({
        ...inputValues,
        ship_to_contact: "",
        ship_to_contact_name: "",
      });
    }
  };

  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Shipping")}
        iconProps={{
          icon: "fa-solid fa-location-dot",
          containerClassName:
            "bg-[linear-gradient(180deg,#909DB71a_0%,#63769A1a_100%)]",
          id: "shipping_card_icon",
          colors: ["#909DB7", "#63769A"],
        }}
        children={
          <div className="pt-2">
            <div className="flex 2xl:flex-row flex-col gap-2 mt-[3px] items-start">
              <ul className="w-full flex flex-col gap-1">
                <li>
                  <SelectField
                    label={_t("Ship To")}
                    placeholder={_t("Select Ship To")}
                    labelPlacement="left"
                    editInline={true}
                    iconView={true}
                    readOnly={isReadOnly}
                    fixStatus={getStatusForField(loadingStatus, "ship_to")}
                    options={shipToOption}
                    allowClear
                    onClear={() => {
                      handleUpdateField({
                        ship_to: "",
                        po_address1: "",
                        po_address2: "",
                        po_city: "",
                        po_state: "",
                        po_zip: "",
                        ship_to_contact: 0,
                        ship_to_contact_additional_contact_id: 0,
                        ship_to_contact_image: "",
                        ship_to_contact_name: "",
                      });
                    }}
                    onChange={(value: string | string[]) => {
                      const newpayload =
                        value == "po_pick_up"
                          ? {
                              po_address1: "",
                              po_address2: "",
                              po_city: "",
                              po_state: "",
                              po_zip: "",
                            }
                          : value == "directory_contact"
                          ? {
                              ship_to_contact: 0,
                              ship_to_contact_additional_contact_id: 0,
                              ship_to_contact_image: "",
                              ship_to_contact_name: "",
                            }
                          : {};
                      handleUpdateField({
                        ship_to: Array.isArray(value) ? value?.[0] : value,
                        purchase_order_id: Number(purchase_order_id) || 0,
                        ...newpayload,
                        // customer_id: inputValues?.customer_id || "",
                      });
                      // handleChange({ value, name: "ship_to" });
                    }}
                    value={
                      purchaseOrderDetail?.ship_to &&
                      purchaseOrderDetail?.ship_to != ""
                        ? purchaseOrderDetail?.ship_to
                        : undefined
                    }
                  />
                </li>
                {purchaseOrderDetail?.ship_to === "directory_contact" ? (
                  <li className="overflow-hidden">
                    <ButtonField
                      label={_t("Ship to Contact")}
                      placeholder={_t("Select Contact")}
                      labelPlacement="left"
                      editInline={true}
                      iconView={true}
                      required={false}
                      readOnly={isReadOnly}
                      onClick={() => {
                        setIsOpenShipToContact(true);
                      }}
                      value={HtmlDecode(
                        inputValues?.ship_to_contact_name || ""
                      )}
                      avatarProps={{
                        user: {
                          name: HtmlDecode(
                            purchaseOrderDetail?.ship_to_contact_name || ""
                          ),
                          image: purchaseOrderDetail?.ship_to_contact_image,
                        },
                      }}
                      statusProps={{
                        status: getStatusForField(
                          loadingStatus,
                          "ship_to_contact"
                        ),
                      }}
                      disabled={
                        getStatusForField(loadingStatus, "ship_to_contact") ===
                        "loading"
                      }
                      rightIcon={
                        inputValues?.ship_to_contact &&
                        loadingStatus?.find(
                          (el) => el.field === "ship_to_contact"
                        )?.status != "loading" ? (
                          <div className="flex items-center gap-1">
                            <ContactDetailsButton
                              onClick={async () => {
                                setAdditionContact(
                                  Number(
                                    inputValues?.ship_to_contact_additional_contact_id
                                  ) || 0
                                );
                                setIsOpenProjectManagerDetails(true);
                              }}
                            />
                            <DirectoryFieldRedirectionIcon
                              directoryId={
                                inputValues?.ship_to_contact?.toString() || ""
                              }
                              directoryTypeKey={getDirectaryKeyById(
                                inputValues.supplier_dir_type === 1
                                  ? 2
                                  : Number(inputValues.supplier_dir_type),
                                gConfig
                              )}
                            />
                          </div>
                        ) : (
                          <></>
                        )
                      }
                    />
                  </li>
                ) : (
                  ""
                )}
                <li>
                  <DatePickerField
                    label={_t("Delivery Date")}
                    labelPlacement="left"
                    placeholder={_t("Select Delivery Date")}
                    editInline={true}
                    value={
                      inputValues?.delivery_date
                        ? displayDateFormat(
                            inputValues?.delivery_date?.toString()?.trim(),
                            date_format
                          )
                        : null
                    }
                    readOnly={isReadOnly}
                    iconView={true}
                    inputReadOnly={true}
                    allowClear={true}
                    format={date_format}
                    fixStatus={getStatusForField(
                      loadingStatus,
                      "delivery_date"
                    )}
                    onChange={(_, dateString) => {
                      // if (purchaseOrderDetail?.delivery_date !== dateString) {
                      //   handleChangeDate(dateString as string, "delivery_date");
                      // }
                      if (!!dateString) {
                        const deliveryDate = inputValues?.po_order_date
                          ? dayjs(inputValues?.po_order_date, date_format)
                          : null;
                        const expireDate = dayjs(
                          dateString?.toString(),
                          date_format
                        );

                        if (deliveryDate && deliveryDate?.isAfter(expireDate)) {
                          notification.error({
                            description: _t(
                              "Delivery Date must be greater than or equal to Order Date."
                            ),
                          });
                          handleChangeFieldStatus({
                            field: "delivery_date",
                            status: "error",
                          });
                          resetFieldStatus("delivery_date");
                          return;
                        }
                        handleChangeDate(
                          dateString?.toString(),
                          "delivery_date"
                        );
                      } else {
                        handleChangeDate("", "delivery_date");
                      }
                    }}
                  />
                </li>
                <li className="overflow-hidden">
                  <InputField
                    label={_t("Shipped Via")}
                    placeholder={_t("Shipped Via")}
                    labelPlacement="left"
                    editInline={true}
                    iconView={true}
                    value={HtmlDecode(inputValues?.ship_via || "")}
                    onChange={(e) => handleInpOnChange(e, "ship_via")}
                    readOnly={isReadOnly}
                    fixStatus={getStatusForField(loadingStatus, "ship_via")}
                    onMouseEnter={() => {
                      handleChangeFieldStatus({
                        field: "ship_via",
                        status: "edit",
                        action: "ME",
                      });
                    }}
                    onMouseLeaveDiv={() => {
                      handleChangeFieldStatus({
                        field: "ship_via",
                        status: "button",
                        action: "ML",
                      });
                    }}
                    onFocus={() =>
                      handleChangeFieldStatus({
                        field: "ship_via",
                        status: "save",
                        action: "FOCUS",
                      })
                    }
                    onBlur={(e) => {
                      const value = e?.target?.value.trim();
                      if (value !== purchaseOrderDetail?.ship_via) {
                        handleUpdateField({ ship_via: value });
                      } else {
                        handleChangeFieldStatus({
                          field: "ship_via",
                          status: "button",
                          action: "BLUR",
                        });
                        setInputValues({
                          ...inputValues,
                          ship_via: purchaseOrderDetail.ship_via,
                        });
                      }
                    }}
                  />
                </li>
                <li className="overflow-hidden">
                  <InputField
                    label={_t("FOB Point")}
                    placeholder={_t("FOB Point")}
                    value={HtmlDecode(inputValues?.fob_point)}
                    labelPlacement="left"
                    editInline={true}
                    iconView={true}
                    onChange={(e) => handleInpOnChange(e, "fob_point")}
                    readOnly={isReadOnly}
                    fixStatus={getStatusForField(loadingStatus, "fob_point")}
                    onMouseEnter={() => {
                      handleChangeFieldStatus({
                        field: "fob_point",
                        status: "edit",
                        action: "ME",
                      });
                    }}
                    onMouseLeaveDiv={() => {
                      handleChangeFieldStatus({
                        field: "fob_point",
                        status: "button",
                        action: "ML",
                      });
                    }}
                    onFocus={() =>
                      handleChangeFieldStatus({
                        field: "fob_point",
                        status: "save",
                        action: "FOCUS",
                      })
                    }
                    onBlur={(e) => {
                      const value = e?.target?.value.trim();
                      if (value !== purchaseOrderDetail?.fob_point) {
                        if (Number(value) < 0) {
                          notification.error({
                            description: _t(
                              "FOB point must be greater than zero"
                            ),
                          });
                          dispatch(
                            updatePODetail({
                              fob_point: purchaseOrderDetail?.fob_point,
                            })
                          );
                          return;
                        } else {
                          handleUpdateField({ fob_point: value });
                        }
                      } else {
                        handleChangeFieldStatus({
                          field: "fob_point",
                          status: "button",
                          action: "BLUR",
                        });
                        setInputValues({
                          ...inputValues,
                          fob_point: purchaseOrderDetail?.fob_point,
                        });
                      }
                    }}
                  />
                </li>
              </ul>
              {purchaseOrderDetail?.ship_to === "po_pick_up" ? (
                <AddressInformationField isReadOnly={isReadOnly} />
              ) : (
                <></>
              )}
            </div>
          </div>
        }
      />

      {isOpenProjectManagerDetails && (
        <ContactDetailsModal
          isOpenContact={isOpenProjectManagerDetails}
          onCloseModal={() => setIsOpenProjectManagerDetails(false)}
          contactId={Number(inputValues?.ship_to_contact)}
          additional_contact_id={additionalContact}
        />
      )}
      {isOpenShipToContact && (
        <SelectCustomerDrawer
          projectId={Number(inputValues?.ship_to_contact)}
          openSelectCustomerSidebar={isOpenShipToContact}
          closeDrawer={() => {
            // dispatch(setActiveField(CFConfig.customer_key));
            setIsOpenShipToContact(false);
          }}
          singleSelecte={true}
          options={shipToContactOptionKey}
          setCustomer={(data) => {
            handleShiptoContact(
              data?.length ? (data[0] as Partial<TselectedContactSendMail>) : {}
            );
          }}
          selectedCustomer={
            inputValues.ship_to_contact &&
            inputValues?.ship_to_contact !== null &&
            inputValues?.ship_to_contact_name &&
            inputValues?.ship_to_contact_name !== ""
              ? ([
                  {
                    display_name: inputValues?.ship_to_contact_name,
                    image: inputValues?.ship_to_contact_image || "",
                    user_id: inputValues?.ship_to_contact,
                    // customer_id: inputValues?.customer_id,
                    contact_id: inputValues?.ship_to_contact,
                    type: inputValues?.supplier_dir_type,
                    type_key: getDirectaryKeyById(
                      inputValues?.supplier_dir_type === 1
                        ? 2
                        : Number(inputValues?.supplier_dir_type),
                      gConfig
                    ),
                  },
                ] as TselectedContactSendMail[])
              : []
          }
          // selectedCustomer={isCustomerVal as TselectedContactSendMail[]}
          groupCheckBox={true}
          additionalContactDetails={1}
          app_access={false}
          canWrite={false}
        />
      )}

      {/* {isContactDetails && (
        <ContactDetailsModal
          isOpenContact={isContactDetails}
          onCloseModal={() => {
            setcontactId(0);
            setIsContactDetails(false);
          }}
          contactId={contactId}
          readOnly={isReadOnly}
          additional_contact_id={additionalContact}
        />
      )} */}
    </>
  );
};

export default ShippingCard;
